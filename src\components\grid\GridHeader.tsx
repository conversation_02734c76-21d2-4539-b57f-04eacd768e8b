
import React, { useState, useEffect } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import SectionTitle from "@/features/post/components/SectionTitle";

export interface GridHeaderProps {
    length: number;
    itemsPerPage: number;
    title?: string;
    showPagination?: boolean;
    onPrevious?: () => void;
    onNext?: () => void;
    // Optional props for inline editing in builder context
    isEditable?: boolean;
    onTitleChange?: (newTitle: string) => void;
}

export const GridHeader: React.FC<GridHeaderProps> = ({
    length = 0,
    itemsPerPage,
    title,
    showPagination = true,
    onPrevious,
    onNext,
    isEditable = false,
    onTitleChange,
}) => {
    const [currentPage, setCurrentPage] = useState(0);

    // Reset pagination when links data changes
    useEffect(() => {
        setCurrentPage(0);
    }, [length]);

    if (length === 0) {
        return null;
    }

    const totalPages = Math.ceil(length / itemsPerPage);

    const handlePrevious = () => {
        setCurrentPage((prev) => (prev === 0 ? totalPages - 1 : prev - 1));
        onPrevious?.();
    };

    const handleNext = () => {
        setCurrentPage((prev) => (prev === totalPages - 1 ? 0 : prev + 1));
        onNext?.();
    };

    return (
        <div className="flex items-center justify-between mb-6 space-x-6">
            <div>
                <SectionTitle
                    title={title || ""}
                    className="py-4"
                    isEditable={isEditable}
                    onTitleChange={onTitleChange}
                />
            </div>

            <div className="flex-grow border-t border-gray-500" />

            {showPagination && (
                <div className="flex items-center gap-2">
                    <Button
                        className="rounded-full border border-blue-700"
                        variant="ghost"
                        size="sm"
                        onClick={handlePrevious}
                        disabled={totalPages <= 1}
                    >
                        <ChevronLeft className="h-4 w-4 text-blue-700" />
                    </Button>

                    <span className="text-sm text-gray-600 px-2">
                        {currentPage + 1} / {totalPages}
                    </span>

                    <Button
                        className="rounded-full border border-blue-700"
                        variant="ghost"
                        size="sm"
                        onClick={handleNext}
                        disabled={totalPages <= 1}
                    >
                        <ChevronRight className="h-4 w-4 text-blue-700" />
                    </Button>
                </div>
            )}
        </div>
    );
};

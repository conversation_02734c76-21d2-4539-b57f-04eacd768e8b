import { API_URL, BaseResponse, restApi } from "@/api/restApi";
import { API_ENDPOINTS } from "@/api/endpoints";
import type {
  Post,
  PostType,
  PostStatus,
} from "../../builder/components/nodes/templates/post/states/types";

export interface PostQueryParams {
  type: PostType;
  status: PostStatus;
  page: number;
  size: number;
  keyword: string;
}

const PUBLIC_POST_URL = API_ENDPOINTS.PORTAL.PUBLIC.POSTS;
const ADMIN_POST_URL = API_ENDPOINTS.PORTAL.ADMIN.POSTS;

// L<PERSON>y danh sách bài viết (có filter)
export async function fetchFeaturedPosts(
  params: PostQueryParams
): Promise<BaseResponse<Post[]>> {
  const res = await restApi.get<BaseResponse<Post[]>>(
    `${API_URL}${PUBLIC_POST_URL}/featured`,
    {
      params,
    }
  );
  return res.data;
}

// L<PERSON><PERSON> danh sách bài viết (có filter) - For admin panel
export async function fetchPosts(
  params: PostQueryParams
): Promise<BaseResponse<Post[]>> {
  const res = await restApi.get<BaseResponse<Post[]>>(
    `${API_URL}${ADMIN_POST_URL}/filter`,
    {
      params,
    }
  );
  return res.data;
}

// Lấy danh sách bài viết (có filter) - For admin panel
export async function fetchPublicPosts(): Promise<BaseResponse<Post[]>> {
  const res = await restApi.get<BaseResponse<Post[]>>(
    `${API_URL}${PUBLIC_POST_URL}`
  );
  return res.data;
}

// Lấy 1 bài viết chi tiết - For admin panel
export async function fetchPost(id: number): Promise<BaseResponse<Post>> {
  const res = await restApi.get<BaseResponse<Post>>(
    `${API_URL}${ADMIN_POST_URL}/${id}`
  );
  return res.data;
}

// Tạo mới bài viết
export async function createPost(
  payload: Omit<Post, "id" | "createdAt" | "updatedAt" | "publishedAt">
): Promise<BaseResponse<Post>> {
  const res = await restApi.post<BaseResponse<Post>>(
    `${API_URL}${ADMIN_POST_URL}`,
    payload
  );
  return res.data;
}

// Cập nhật bài viết (không cho update các trường trạng thái, author, type, id, publishedAt)
export async function updatePost(
  id: number,
  payload: Omit<
    Post,
    | "id"
    | "createdAt"
    | "updatedAt"
    | "status"
    | "postType"
    | "authorId"
    | "publishedAt"
  >
): Promise<BaseResponse<Post>> {
  const res = await restApi.put<BaseResponse<Post>>(
    `${API_URL}${ADMIN_POST_URL}/${id}`,
    payload
  );
  return res.data;
}

// Cập nhật trạng thái (status) bài viết (ngoại trừ approve/reject)
export async function updatePostStatus(
  id: number,
  status: PostStatus
): Promise<BaseResponse<void>> {
  const res = await restApi.put<BaseResponse<void>>(
    `${API_URL}${ADMIN_POST_URL}/${id}/status/${status}`
  );
  return res.data;
}

// Duyệt bài viết (approve)
export async function approvePost(id: number): Promise<BaseResponse<void>> {
  const res = await restApi.put<BaseResponse<void>>(
    `${API_URL}${ADMIN_POST_URL}/${id}/approve`
  );
  return res.data;
}

// Từ chối duyệt (reject)
export async function rejectPost(id: number): Promise<BaseResponse<void>> {
  const res = await restApi.put<BaseResponse<void>>(
    `${API_URL}${ADMIN_POST_URL}/${id}/reject`
  );
  return res.data;
}

// Check if slug exists
export async function checkSlugExists(
  slug: string
): Promise<BaseResponse<boolean>> {
  const res = await restApi.get<BaseResponse<boolean>>(
    `${API_URL}${ADMIN_POST_URL}/exists-by-slug/${slug}`
  );
  return res.data;
}

// Generate slug from title utility function
export function generateSlugFromTitle(title: string): string {
  // 1. Chuẩn hóa, chuyển về lower case, xóa khoảng trắng đầu/cuối
  let slug = title.trim().toLowerCase();
  // 2. Chuyển "đ" và "Đ" thành "d"
  slug = slug.replace(/đ/g, "d").replace(/Đ/g, "d");
  // 3. Bỏ dấu tiếng Việt
  slug = slug.normalize("NFD").replace(/[\u0300-\u036f]/g, "");
  // 4. Bỏ ký tự đặc biệt, chỉ giữ a-z, 0-9, gạch ngang, khoảng trắng
  slug = slug.replace(/[^a-z0-9\s-]/g, "");
  // 5. Đổi khoảng trắng thành gạch ngang, gom gạch ngang liên tiếp
  slug = slug.replace(/\s+/g, "-").replace(/-+/g, "-");
  // 6. Bỏ gạch ngang đầu/cuối (nếu có)
  slug = slug.replace(/^-+|-+$/g, "");

  return slug;
}

// Generate unique slug with fallback suffix if needed
export async function generateUniqueSlug(title: string): Promise<string> {
  const baseSlug = generateSlugFromTitle(title);

  try {
    // Check if base slug is available
    const response = await checkSlugExists(baseSlug);
    if (!response.data) {
      // Slug is available, return as is
      return baseSlug;
    }

    // If slug exists, try with number suffix
    for (let i = 2; i <= 10; i++) {
      const numberedSlug = `${baseSlug}-${i}`;
      const numberedResponse = await checkSlugExists(numberedSlug);
      if (!numberedResponse.data) {
        return numberedSlug;
      }
    }

    // If all numbered versions fail, use random suffix as final fallback
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    return `${baseSlug}-${randomSuffix}`;
  } catch (error) {
    console.error("Error checking slug availability:", error);
    // Fallback to original behavior on API error
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    return `${baseSlug}-${randomSuffix}`;
  }
}

// Fetch posts with specific type (for pages, articles, etc.) - Admin endpoint
export async function fetchPostsByType(
  type: PostType
): Promise<BaseResponse<Post[]>> {
  const res = await restApi.get<BaseResponse<Post[]>>(
    `${API_URL}${ADMIN_POST_URL}/filter`,
    {
      params: { type },
    }
  );
  return res.data;
}

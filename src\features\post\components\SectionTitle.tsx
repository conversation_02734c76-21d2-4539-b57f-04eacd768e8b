import React, { useCallback } from "react";
import { cn } from "@/lib/utils";
import { useInlineTextEditor } from "@/features/builder/hooks/useInlineTextEditor";

interface SectionTitleProps {
  title: string;
  className?: string;
  isEditable?: boolean;
  onTitleChange?: (newTitle: string) => void;
}

const SectionTitle: React.FC<SectionTitleProps> = ({
  title,
  className,
  isEditable = false,
  onTitleChange
}) => {
  const handleSave = useCallback((newTitle: string) => {
    onTitleChange?.(newTitle);
  }, [onTitleChange]);

  const {
    isEditing,
    elementRef,
    startEditing,
    handleKeyDown,
    handleBlur
  } = useInlineTextEditor({
    initialContent: title,
    onSave: handleSave,
    multiline: false
  });

  if (isEditable && onTitleChange) {
    return (
      <div className={cn("flex items-center gap-4", className)}>
        <h2
          ref={elementRef}
          className={cn(
            "text-2xl lg:text-3xl font-medium whitespace-nowrap outline-none",
            isEditing && "ring-2 ring-blue-500 ring-opacity-50 rounded px-1"
          )}
          contentEditable={isEditing}
          suppressContentEditableWarning={true}
          onDoubleClick={startEditing}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
          style={{
            minHeight: '1em',
            whiteSpace: 'pre-wrap'
          }}
        >
          {title || '\u00A0'}
        </h2>
      </div>
    );
  }

  // Non-editable version (backward compatibility)
  return (
    <div className={cn("flex items-center gap-4", className)}>
      <h2 className="text-2xl lg:text-3xl font-medium whitespace-nowrap">
        {title}
      </h2>
    </div>
  );
};

export default SectionTitle;

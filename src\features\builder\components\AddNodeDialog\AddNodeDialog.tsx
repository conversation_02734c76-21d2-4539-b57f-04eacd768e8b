import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Search, Frame, Type, Image, Video, ImageIcon, Calendar, X, Columns2, Library, Newspaper, BarChart3, RotateCcw, Heading } from 'lucide-react';
import { TreeNodeType, TreeNodeGroup } from '../../context/types';
import { cn } from '@/lib/utils';
import { searchWithoutTones } from '@/lib/vietnamese';

interface NodeTypeDefinition {
  type: TreeNodeType;
  group: TreeNodeGroup;
  label: string;
  icon: React.ReactNode;
  description: string;
}

const nodeTypes: NodeTypeDefinition[] = [
  // Basic nodes
  {
    type: 'frame',
    group: 'basic',
    label: 'Frame',
    icon: <Frame className="w-5 h-5" />,
    description: 'Container cho các node khác'
  },
  {
    type: 'text',
    group: 'basic',
    label: 'Text',
    icon: <Type className="w-5 h-5" />,
    description: 'Văn bản'
  },
  {
    type: 'image',
    group: 'basic',
    label: 'Hình ảnh',
    icon: <Image className="w-5 h-5" />,
    description: 'Hiển thị hình ảnh'
  },
  {
    type: 'video',
    group: 'basic',
    label: 'Video',
    icon: <Video className="w-5 h-5" />,
    description: 'Hiển thị video'
  },
  {
    type: 'gridHeader',
    group: 'basic',
    label: 'Tiêu đề',
    icon: <Heading className="w-5 h-5" />,
    description: 'Tiêu đề nội dung'
  },

  // Template nodes
  {
    type: 'imageWithCaption',
    group: 'template',
    label: 'Ảnh với chú thích',
    icon: <ImageIcon className="w-5 h-5" />,
    description: 'Hình ảnh kèm mô tả'
  },

  // Widget nodes
  {
    type: 'dateTimeCard',
    group: 'widget',
    label: 'Đồng hồ',
    icon: <Calendar className="w-5 h-5" />,
    description: 'Hiển thị ngày giờ'
  },
  {
    type: 'doublePanelsCard',
    group: 'template',
    label: 'Thẻ hai mặt',
    icon: <Columns2 className="w-5 h-5" />,
    description: 'Tạo thẻ 2 mặt trái phải'
  },
  {
    type: 'libraryDisplay',
    group: 'template',
    label: 'Thư viện nổi bật',
    icon: <Library className="w-5 h-5" />,
    description: 'Xem những thư viện nổi bật'
  },
  {
    type: 'newsCustomGridDisplay',
    group: 'template',
    label: 'Danh sách bài viết 4 bài',
    icon: <Newspaper className="w-5 h-5" />,
    description: 'Tạo dòng hiển thị danh sách bài viết, với giao diện UI cố định'
  },
  {
    type: 'newsGridDisplay',
    group: 'template',
    label: 'Danh sách bài viết',
    icon: <Newspaper className="w-5 h-5" />,
    description: 'Tạo dòng hiển thị danh sách bài viết'
  },
  {
    type: 'statCard',
    group: 'template',
    label: 'Danh sách thống kê',
    icon: <BarChart3 className="w-5 h-5" />,
    description: 'Tạo danh sách thẻ thống kê'
  },
  {
    type: 'postCarousel',
    group: 'template',
    label: 'Danh sách chi tiết bài viết',
    icon: <RotateCcw className="w-5 h-5" />,
    description: 'Danh sách chi tiết bài viết quay ngang theo chiều dọc'
  },
];

interface AddNodeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAddNode: (type: TreeNodeType) => void;
}

export const AddNodeDialog: React.FC<AddNodeDialogProps> = ({
  open,
  onOpenChange,
  onAddNode
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTab, setSelectedTab] = useState<TreeNodeGroup>('basic');

  // Close on Escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && open) {
        onOpenChange(false);
      }
    };
    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [open, onOpenChange]);

  // Prevent body scroll when dialog is open
  useEffect(() => {
    if (open) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [open]);

  if (!open) return null;

  const filteredNodes = nodeTypes.filter(node => {
    if (!searchTerm) return true;
    return searchWithoutTones(searchTerm, node.label) ||
      searchWithoutTones(searchTerm, node.description);
  });

  const getNodesByGroup = (group: TreeNodeGroup) =>
    filteredNodes.filter(node => node.group === group);

  const handleSelectNode = (type: TreeNodeType) => {
    onAddNode(type);
    onOpenChange(false);
    setSearchTerm('');
  };

  return createPortal(
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 z-50 animate-in fade-in duration-200"
        onClick={() => onOpenChange(false)}
      />

      {/* Dialog */}
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-xl shadow-2xl w-full max-w-3xl animate-in zoom-in-95 duration-200">
          {/* Header */}
          <div className="relative px-6 py-5 border-b bg-gradient-to-r from-blue-50 via-white to-purple-50 rounded-t-xl">
            <h2 className="text-2xl font-semibold text-gray-800">Thêm Node Mới</h2>
            <p className="text-sm text-gray-600 mt-1">Chọn loại node bạn muốn thêm vào canvas</p>
            <button
              title='Đóng'
              onClick={() => onOpenChange(false)}
              className="absolute right-4 top-4 rounded-full p-2 hover:bg-gray-100 transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>

          <div className="p-6">
            {/* Search */}
            <div className="relative mb-6">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                placeholder="Tìm kiếm node (hỗ trợ không dấu)..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-11 h-12 text-base"
                autoFocus
              />
            </div>

            {/* Show tabs only when not searching */}
            {!searchTerm ? (
              <Tabs value={selectedTab} onValueChange={(value) => setSelectedTab(value as TreeNodeGroup)}>
                <TabsList className="w-full grid grid-cols-3 h-12 mb-6">
                  <TabsTrigger value="basic" className="text-base">Cơ bản</TabsTrigger>
                  <TabsTrigger value="template" className="text-base">Mẫu</TabsTrigger>
                  <TabsTrigger value="widget" className="text-base">Tiện ích</TabsTrigger>
                </TabsList>

                {/* Fixed height content area */}
                <div className="h-[450px] overflow-y-auto pr-2">
                  <TabsContent value="basic" className="mt-0">
                    <NodeGrid nodes={getNodesByGroup('basic')} onSelect={handleSelectNode} />
                  </TabsContent>

                  <TabsContent value="template" className="mt-0">
                    <NodeGrid nodes={getNodesByGroup('template')} onSelect={handleSelectNode} />
                  </TabsContent>

                  <TabsContent value="widget" className="mt-0">
                    <NodeGrid nodes={getNodesByGroup('widget')} onSelect={handleSelectNode} />
                  </TabsContent>
                </div>
              </Tabs>
            ) : (
              /* Show all results when searching - Same height as tabs view */
              <div className="h-[498px] overflow-y-auto pr-2">
                {filteredNodes.length > 0 ? (
                  <>
                    {/* Basic nodes */}
                    {getNodesByGroup('basic').length > 0 && (
                      <div className="mb-6">
                        <h3 className="text-sm font-semibold text-gray-600 mb-3">Cơ bản</h3>
                        <NodeGrid nodes={getNodesByGroup('basic')} onSelect={handleSelectNode} />
                      </div>
                    )}

                    {/* Template nodes */}
                    {getNodesByGroup('template').length > 0 && (
                      <div className="mb-6">
                        <h3 className="text-sm font-semibold text-gray-600 mb-3">Mẫu</h3>
                        <NodeGrid nodes={getNodesByGroup('template')} onSelect={handleSelectNode} />
                      </div>
                    )}

                    {/* Widget nodes */}
                    {getNodesByGroup('widget').length > 0 && (
                      <div className="mb-6">
                        <h3 className="text-sm font-semibold text-gray-600 mb-3">Tiện ích</h3>
                        <NodeGrid nodes={getNodesByGroup('widget')} onSelect={handleSelectNode} />
                      </div>
                    )}
                  </>
                ) : (
                  <NodeGrid nodes={[]} onSelect={handleSelectNode} />
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </>,
    document.body
  );
};

interface NodeGridProps {
  nodes: NodeTypeDefinition[];
  onSelect: (type: TreeNodeType) => void;
}

const NodeGrid: React.FC<NodeGridProps> = ({ nodes, onSelect }) => {
  if (nodes.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-gray-500">
        <Search className="w-12 h-12 mb-4 text-gray-300" />
        <p className="text-lg">Không tìm thấy node nào</p>
        <p className="text-sm mt-1">Thử tìm kiếm với từ khóa khác</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 gap-4">
      {nodes.map((node) => (
        <button
          key={node.type}
          className={cn(
            "p-6 border-2 rounded-xl text-left",
            "hover:border-blue-400 hover:bg-blue-50/50 hover:shadow-lg",
            "transition-all duration-200",
            "group focus:outline-none focus:ring-2 focus:ring-blue-500"
          )}
          onClick={() => onSelect(node.type)}
        >
          <div className="flex items-start gap-4">
            <div className="p-3 rounded-lg bg-blue-100 text-blue-600 group-hover:bg-blue-600 group-hover:text-white transition-colors">
              {node.icon}
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-800 mb-1">
                {node.label}
              </h3>
              <p className="text-sm text-gray-600">
                {node.description}
              </p>
            </div>
          </div>
        </button>
      ))}
    </div>
  );
};
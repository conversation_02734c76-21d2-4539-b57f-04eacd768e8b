import React, { useState, useEffect, useCallback } from "react";
import { NodeRendererProps } from "../../NodeFactory";
import { cn } from "@/lib/utils";
import { compareNodeWithProperties } from '@/lib/memo-utils';
import { NewsArticleCard } from "@/features/demo/homepage/components/NewsArticleCard";
import { Post } from "@/features/builder/components/nodes/templates/post/states/types";
import { GridHeader } from "@/components/grid/GridHeader";
import { GridPagination } from "@/components/grid/GridPagination";
import { fetchPublicPosts } from "@/features/post/states/api";
import { mockPosts } from "@/features/post/states/mockData";
import { useNodeOperations } from "../../../../context/hooks";

const NewsGridDisplayNodeComponent: React.FC<NodeRendererProps & React.HTMLAttributes<HTMLElement>> = ({
  node,
  isPreview = false,
  ...htmlProps
}) => {
  const { updateNode } = useNodeOperations();
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(0);

  // Get numberOfPosts from node properties, default to 3
  const numberOfPosts = (node.properties?.numberOfPosts as number) || 3;
  // Get header from node properties, default to "TIN TRONG NGÀNH"
  const header = (node.properties?.header as string) || "TIN TRONG NGÀNH";

  // Handle header change from inline editing
  const handleHeaderChange = useCallback((newHeader: string) => {
    updateNode(node.id, {
      properties: {
        ...node.properties,
        header: newHeader
      }
    });
  }, [updateNode, node.id, node.properties]);

  // Fetch posts data
  useEffect(() => {
    const loadPosts = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await fetchPublicPosts();
        if (response.data) {
          setPosts(response.data);
        } else {
          setPosts([]);
        }
      } catch (err) {
        console.error('Failed to fetch posts:', err);
        console.log('Using fallback mock data due to API failure');
        // Use mock data as fallback when API fails
        setPosts(mockPosts);
        setError(null); // Clear error since we have fallback data
      } finally {
        setLoading(false);
      }
    };

    loadPosts();
  }, []);

  // Filter published posts
  const publishedPosts = posts.filter(post => post.status.includes(""));

  // Handle loading state
  if (loading) {
    return (
      <div
        className={cn("flex items-center justify-center py-12", node.style?.className)}
        style={node.style?.css}
        {...htmlProps}
      >
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-3 text-gray-600">Đang tải bài viết...</span>
      </div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <div
        className={cn("text-center py-8 text-red-600", node.style?.className)}
        style={node.style?.css}
        {...htmlProps}
      >
        <p>{error}</p>
      </div>
    );
  }

  // Handle empty state
  if (publishedPosts.length === 0) {
    return (
      <div
        className={cn("text-center py-8", node.style?.className)}
        style={node.style?.css}
        {...htmlProps}
      >
        <p className="text-gray-500">Không có bài viết nào để hiển thị</p>
      </div>
    );
  }

  const totalPages = Math.ceil(publishedPosts.length / numberOfPosts);
  const startIndex = currentPage * numberOfPosts;
  const endIndex = startIndex + numberOfPosts;
  const currentPosts = publishedPosts.slice(startIndex, endIndex);

  const handlePrevious = () => {
    setCurrentPage((prev) => (prev === 0 ? totalPages - 1 : prev - 1));
  };

  const handleNext = () => {
    setCurrentPage((prev) => (prev === totalPages - 1 ? 0 : prev + 1));
  };

  const handleArticleClick = (post: Post) => {
    // In preview mode, don't navigate
    if (isPreview) return;

    // In builder mode, you might want to handle this differently
    console.log('Article clicked:', post.title);
  };

  return (
    <div
      className={cn("space-y-6", node.style?.className)}
      style={node.style?.css}
      {...htmlProps}
    >
      {/* Header with navigation */}
      <GridHeader
        length={publishedPosts.length}
        itemsPerPage={numberOfPosts}
        title={header}
        onPrevious={handlePrevious}
        onNext={handleNext}
        isEditable={!isPreview}
        onTitleChange={handleHeaderChange}
      />

      {/* Grid layout */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {currentPosts.map((post) => (
          <NewsArticleCard
            key={post.id}
            post={post}
            size="medium"
            onClick={() => handleArticleClick(post)}
          />
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <GridPagination
          length={publishedPosts.length}
          itemsPerPage={numberOfPosts}
          onChangePagination={(index) => setCurrentPage(index)}
        />
      )}
    </div>
  );
};

// Memoized template node to prevent re-renders
export const NewsGridDisplayNode = React.memo(
  NewsGridDisplayNodeComponent,
  compareNodeWithProperties
);

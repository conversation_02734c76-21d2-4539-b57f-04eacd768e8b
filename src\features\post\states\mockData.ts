import {
  Post,
  PostType,
  PostStatus,
} from "../../builder/components/nodes/templates/post/states/types";

// <PERSON><PERSON> posts data
export const mockPosts: Post[] = [
  {
    id: 1,
    createdAt: Date.now() - 7 * 24 * 60 * 60 * 1000, // 7 days ago
    updatedAt: Date.now() - 2 * 24 * 60 * 60 * 1000, // 2 days ago
    publishedAt: Date.now() - 2 * 24 * 60 * 60 * 1000,
    title: "Bài viết thứ 1",
    slug: "bai-viet-thu-1",
    content: {
      type: "root",
      children: [
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "Nội dung bài viết thứ nhất...",
              children: [],
            },
          ],
        },
      ],
    },
    excerpt: {
      image: "src/assets/images/banner-01.jpg",
      description:
        "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.",
      files: [],
    },
    postType: "ARTICLE" as PostType,
    status: "PUBLISHED" as PostStatus,
    authorId: "user-123",
    parentId: null,
  },
  {
    id: 2,
    createdAt: Date.now() - 14 * 24 * 60 * 60 * 1000, // 14 days ago
    updatedAt: Date.now() - 5 * 24 * 60 * 60 * 1000, // 5 days ago
    publishedAt: Date.now() - 5 * 24 * 60 * 60 * 1000,
    title: "Bài viết thứ 2",
    slug: "bai-viet-thu-2",
    content: {
      type: "root",
      children: [
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "Nội dung bài viết thứ hai...",
              children: [],
            },
          ],
        },
      ],
    },
    excerpt: {
      image: "/images/article-2-thumbnail.jpg",
      description: "Mô tả ngắn gọn về nội dung bài viết thứ hai",
      files: [],
    },
    postType: "PAGE" as PostType,
    status: "PUBLISHED" as PostStatus,
    authorId: "admin-1",
    parentId: null,
  },
  {
    id: 3,
    createdAt: Date.now() - 3 * 24 * 60 * 60 * 1000, // 3 days ago
    updatedAt: Date.now() - 1 * 24 * 60 * 60 * 1000, // 1 day ago
    publishedAt: Date.now() - 2 * 24 * 60 * 60 * 1000,
    title: "Bài viết thứ 3",
    slug: "bai-viet-thu-3",
    content: {
      type: "root",
      children: [
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "Nội dung bài viết thứ ba...",
              children: [],
            },
          ],
        },
      ],
    },
    excerpt: {
      image: "/images/article-3-thumbnail.jpg",
      description: "Mô tả ngắn gọn về nội dung bài viết thứ ba",
      files: [],
    },
    postType: "ARTICLE" as PostType,
    status: "DRAFT" as PostStatus,
    authorId: "editor-1",
    parentId: null,
  },
  {
    id: 4,
    createdAt: Date.now() - 10 * 24 * 60 * 60 * 1000, // 10 days ago
    updatedAt: Date.now() - 3 * 24 * 60 * 60 * 1000, // 3 days ago
    publishedAt: Date.now() - 3 * 24 * 60 * 60 * 1000,
    title: "Bài viết thứ 4",
    slug: "bai-viet-thu-4",
    content: {
      type: "root",
      children: [
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "Nội dung bài viết thứ tư...",
              children: [],
            },
          ],
        },
      ],
    },
    excerpt: {
      image: "/images/article-4-thumbnail.jpg",
      description: "Mô tả ngắn gọn về nội dung bài viết thứ tư",
      files: [],
    },
    postType: "PAGE" as PostType,
    status: "PUBLISHED" as PostStatus,
    authorId: "admin-1",
    parentId: null,
  },
  {
    id: 5,
    createdAt: Date.now() - 1 * 24 * 60 * 60 * 1000, // 1 day ago
    updatedAt: Date.now() - 1 * 24 * 60 * 60 * 1000,
    publishedAt: Date.now() - 2 * 24 * 60 * 60 * 1000,
    title: "Bài viết thứ 5",
    slug: "bai-viet-thu-5",
    content: {
      type: "root",
      children: [
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "Nội dung bài viết thứ năm...",
              children: [],
            },
          ],
        },
      ],
    },
    excerpt: {
      image: "/images/article-5-thumbnail.jpg",
      description: "Mô tả ngắn gọn về nội dung bài viết thứ năm",
      files: [],
    },
    postType: "ARTICLE" as PostType,
    status: "REVIEW" as PostStatus,
    authorId: "editor-2",
    parentId: null,
  },
];

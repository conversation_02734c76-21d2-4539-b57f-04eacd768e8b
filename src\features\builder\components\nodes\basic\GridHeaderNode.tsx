import React, { forwardRef, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { NodeRendererProps } from '../NodeFactory';
import { compareNodeWithProperties } from '@/lib/memo-utils';
import { useInlineTextEditor } from '../../../hooks/useInlineTextEditor';
import { useNodeOperations } from '../../../context/hooks';

const GridHeaderNodeComponent = forwardRef<HTMLDivElement, NodeRendererProps & React.HTMLAttributes<HTMLDivElement>>(({
  node,
  isPreview = false,
  ...htmlProps
}, ref) => {
  const { updateNode } = useNodeOperations();
  const title = (node.properties?.title as string) || '';

  const handleSave = useCallback((newTitle: string) => {
    updateNode(node.id, {
      properties: {
        ...node.properties,
        title: newTitle
      }
    });
  }, [updateNode, node.id, node.properties]);

  const {
    isEditing,
    elementRef,
    startEditing,
    handleKeyDown,
    handleBlur
  } = useInlineTextEditor({
    initialContent: title,
    onSave: handleSave,
    multiline: false
  });

  return (
    <div
      ref={ref}
      className={cn(
        "w-full",
        node.style?.className
      )}
      style={node.style?.css}
      {...htmlProps}
    >
      <div className="flex items-center justify-between mb-6 space-x-6">
        <div>
          {/* FIXED: Title text now supports dynamic typography styling */}
          <div
            ref={elementRef}
            className={cn(
              // Default fallback styles - will be overridden by dynamic styles
              "text-3xl font-medium whitespace-nowrap outline-none cursor-pointer",
              // Apply dynamic typography styles from DisplayFormFactory
              node.style?.className,
              // Editing state styles (highest priority)
              isEditing && "ring-2 ring-blue-500 ring-opacity-50 rounded px-1"
            )}
            contentEditable={isEditing}
            suppressContentEditableWarning={true}
            onDoubleClick={isPreview ? undefined : startEditing}
            onKeyDown={handleKeyDown}
            onBlur={handleBlur}
            style={{
              minHeight: '1em',
              whiteSpace: 'pre-wrap'
            }}
          >
            {title || 'Tiêu đề'}
          </div>
        </div>

        <div className="flex-grow border-t border-gray-500" />
      </div>
    </div>
  );
});

GridHeaderNodeComponent.displayName = 'GridHeaderNodeComponent';

// Enable memoization for GridHeaderNode
export const GridHeaderNode = React.memo(GridHeaderNodeComponent, compareNodeWithProperties);

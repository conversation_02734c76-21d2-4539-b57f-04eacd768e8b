import { RootState } from "@/store/rootReducer";
import { PostQueryParams } from "./api";
import { PostStatus } from "../../builder/components/nodes/templates/post/states/types";

// Mock selectors that return default values since Redux slices were removed
export const selectPostStatus = (_state: RootState): PostStatus => "PUBLISHED";
export const selectPostLoading = (_state: RootState): boolean => false;
export const selectPostError = (_state: RootState): string | null => null;
export const selectPostQueryParams = (_state: RootState): PostQueryParams => ({
  type: "ARTICLE",
  status: "PUBLISHED",
  page: 0,
  size: 10,
  keyword: "",
});
export const selectRefetch = (_state: RootState): boolean => false;

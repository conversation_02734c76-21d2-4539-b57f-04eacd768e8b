import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { StatData } from "./states/types";

export interface StatCardItemProps extends StatData {
  onClick?: () => void;
  descriptionClassName?: string;
}

export const StatCardItem: React.FC<StatCardItemProps> = ({
  number,
  name,
  description,
  onClick,
  descriptionClassName,
}) => {
  return (
    <Card
      className={cn(
        "hover:shadow-lg transition-all duration-300 cursor-pointer bg-transparent border-[#BAE4FD] max-w-[200px]"
      )}
      onClick={onClick}
    >
      <CardContent className="p-6 text-center">
        <div className="space-y-2">
          {/* Statistical Number */}
          <div className="text-[64px] font-bold text-[#0177BD] truncate overflow-hidden whitespace-nowrap">
            {number}
          </div>

          {/* Name/Title */}
          <div className="space-y-1.5">
            <h3 className="text-[20px] font-semibold text-gray-900">
              {name}
            </h3>

            <p className={cn(
              // Default fallback styles - will be overridden by dynamic styles
              "text-sm text-gray-600 leading-relaxed",
              // Apply dynamic typography styles from DisplayFormFactory
              descriptionClassName
            )}>
              {description}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

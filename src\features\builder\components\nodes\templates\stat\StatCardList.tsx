import React from "react";
import { compareNodeWithProperties } from '@/lib/memo-utils';
import { NodeRendererProps } from "../../NodeFactory";
import { StatCardItem } from "./StatCard";
import { mockStats } from "./states/mockData";
import { cn } from "@/builder-entry";

const StatCardListNodeComponent: React.FC<NodeRendererProps & React.HTMLAttributes<HTMLElement>> = ({
    node,
    ...htmlProps
}) => {
    return (
        <section
            className={cn("flex flex-wrap gap-4 justify-between", node.style?.className)}
            style={node.style?.css}
            {...htmlProps}
        >
            {mockStats.map((stat, index) => (
                <StatCardItem
                    key={index}
                    number={stat.number}
                    name={stat.name}
                    description={stat.description}
                    descriptionClassName={node.style?.className}
                    onClick={() => {
                        // Optional: Add click handler for individual stats
                        console.log(`Clicked on ${stat.name}`);
                    }}
                />
            ))}
        </section>
    );
};


export const StatCardListNode = React.memo(
    StatCardListNodeComponent,
    compareNodeWithProperties
);

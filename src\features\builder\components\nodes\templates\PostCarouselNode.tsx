import React, { useState, useEffect } from "react";
import { NodeRendererProps } from "../NodeFactory";
import { cn } from "@/lib/utils";
import { compareNodeWithProperties } from '@/lib/memo-utils';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { ImageLoader } from "@/components/image/ImageLoader";
import { fetchPublicPosts } from "@/features/post/states/api";
import { mockPosts } from "@/features/post/states/mockData";
import { useNavigate } from "react-router-dom";
import { Post } from "./post/states/types";

const PostCarouselNodeComponent: React.FC<NodeRendererProps & React.HTMLAttributes<HTMLElement>> = ({
  node,
  isPreview = false,
  ...htmlProps
}) => {
  const navigate = useNavigate();
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  // Fetch posts data
  useEffect(() => {
    const loadPosts = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await fetchPublicPosts();
        if (response.data && response.data.length > 0) {
          setPosts(response.data);
        } else {
          setPosts([]);
        }
      } catch (err) {
        console.error('Failed to fetch posts:', err);
        console.log('Using fallback mock data due to API failure');
        // Use mock data as fallback when API fails
        setPosts(mockPosts);
        setError(null); // Clear error since we have fallback data
      } finally {
        setLoading(false);
      }
    };

    loadPosts();
  }, []);

  // Filter published posts
  const publishedPosts = posts.filter(post => post.status.includes(""));

  // Handle loading state
  if (loading) {
    return (
      <div
        className={cn("flex items-center justify-center py-12", node.style?.className)}
        style={node.style?.css}
        {...htmlProps}
      >
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-3 text-gray-600">Đang tải bài viết...</span>
      </div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <div
        className={cn("text-center py-8 text-red-600", node.style?.className)}
        style={node.style?.css}
        {...htmlProps}
      >
        <p>{error}</p>
      </div>
    );
  }

  // Handle empty state
  if (publishedPosts.length === 0) {
    return (
      <div
        className={cn("text-center py-8", node.style?.className)}
        style={node.style?.css}
        {...htmlProps}
      >
        <p className="text-gray-500">Không có bài viết nào để hiển thị</p>
      </div>
    );
  }

  const currentPost = publishedPosts[currentIndex];

  const handlePrev = () => {
    setCurrentIndex((prev) => (prev > 0 ? prev - 1 : publishedPosts.length - 1));
  };

  const handleNext = () => {
    setCurrentIndex((prev) => (prev + 1) % publishedPosts.length);
  };

  const handleViewMore = () => {
    // In preview mode, don't navigate
    if (isPreview) return;

    // Navigate to post detail page
    navigate(`/posts/${currentPost.slug}`);
  };

  return (
    <div
      className={cn("p-4 space-y-8", node.style?.className)}
      style={node.style?.css}
      {...htmlProps}
    >

      <div className="flex flex-col lg:flex-row rounded-2xl overflow-hidden bg-white max-w-6xl mx-auto gap-2">
        {/* Hình ảnh */}
        <div className="w-full lg:w-1/2 h-64 lg:h-auto overflow-hidden rounded-lg">
          <ImageLoader
            src={currentPost?.excerpt?.image}
            alt={currentPost.title}
            fallbackText="Không có hình ảnh"
            className="w-full h-full object-cover"
          />
        </div>

        {/* Nội dung */}
        <div className="flex flex-col justify-between p-6 text-accent-foreground w-full lg:w-1/2">
          <div>
            <h2 className="text-2xl font-bold mb-4">{currentPost.title}</h2>
            <p
              className={cn(
                // Default fallback styles - will be overridden by dynamic styles
                "text-sm line-clamp-6",
                // Apply dynamic typography styles from DisplayFormFactory
                node.style?.className,
                // Legacy variant support (for backward compatibility)
                node.style?.variants?.descriptionText
              )}
              style={node.style?.variants?.descriptionStyle ? JSON.parse(node.style.variants.descriptionStyle) : undefined}
            >
              {currentPost?.excerpt?.description}
            </p>
          </div>

          <div className="mt-6 space-y-4">
            <Button
              variant="outline"
              className="rounded-full border-2 border-primary text-primary bg-primary-foreground hover:bg-primary/10"
              onClick={handleViewMore}
              disabled={isPreview}
            >
              Xem thêm
            </Button>
            <div className="flex items-center gap-2">
              <button
                title="Bài viết trước"
                onClick={handlePrev}
                className="border border-primary rounded-full p-2 text-primary hover:bg-primary/10"
                disabled={publishedPosts.length <= 1}
              >
                <ChevronLeft className="w-3 h-3" />
              </button>
              <button
                title="Bài viết kế tiếp"
                onClick={handleNext}
                className="border border-primary rounded-full p-2 text-primary hover:bg-primary/10"
                disabled={publishedPosts.length <= 1}
              >
                <ChevronRight className="w-3 h-3" />
              </button>
            </div>

            <div className="flex items-center gap-1">
              {publishedPosts.map((_, idx) => (
                <div
                  key={idx}
                  className={`h-1 rounded-full ${idx === currentIndex ? "w-6 bg-blue-600" : "w-4 bg-gray-400"
                    }`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Memoized template node to prevent re-renders
export const PostCarouselNode = React.memo(
  PostCarouselNodeComponent,
  compareNodeWithProperties
);
